package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"scanner/internal/models"
	"scanner/internal/scanner"
	"scanner/internal/services"
	"scanner/pkg/logger"

	"github.com/gin-gonic/gin"
)

// ScanHandler 扫描处理器
type ScanHandler struct {
	scanService          *services.ScanService
	scanLogService       *services.ScanLogService
	scheduledScanService *services.ScheduledScanService
	engineManager        *scanner.EngineManager
	taskScheduler        *scanner.TaskScheduler
	// 添加结果处理器相关字段
	db                   interface{} // 数据库连接，稍后会具体化
	assetService         interface{} // 资产服务，稍后会具体化
	vulnerabilityService interface{} // 漏洞服务，稍后会具体化
}

// NewScanHandler 创建新的扫描处理器
func NewScanHandler(scanService *services.ScanService, scanLogService *services.ScanLogService, scheduledScanService *services.ScheduledScanService, engineManager *scanner.EngineManager, taskScheduler *scanner.TaskScheduler) *ScanHandler {
	return &ScanHandler{
		scanService:          scanService,
		scanLogService:       scanLogService,
		scheduledScanService: scheduledScanService,
		engineManager:        engineManager,
		taskScheduler:        taskScheduler,
	}
}

// CreateTaskRequest 创建扫描任务请求
type CreateTaskRequest struct {
	Name         string                 `json:"name" binding:"required"`
	Type         string                 `json:"type" binding:"required,oneof=web network host api compliance domain discovery network_discovery port_discovery subdomain_discovery"`
	Description  string                 `json:"description"`
	Targets      []string               `json:"targets" binding:"required"`
	AssetID      uint                   `json:"asset_id"`
	Config       map[string]interface{} `json:"config"`
	Depth        int                    `json:"depth"`
	Timeout      int                    `json:"timeout"`
	Threads      int                    `json:"threads"`
	ScheduleType string                 `json:"schedule_type"`
	ScheduledAt  *time.Time             `json:"scheduled_at"`
	CronExpr     string                 `json:"cron_expr"`
}

// UpdateTaskRequest 更新扫描任务请求
type UpdateTaskRequest struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Status      string                 `json:"status"`
	Config      map[string]interface{} `json:"config"`
	Priority    int                    `json:"priority"`
}

// TaskListResponse 任务列表响应
type TaskListResponse struct {
	Tasks      []*models.ScanTask `json:"tasks"`
	Total      int64              `json:"total"`
	Page       int                `json:"page"`
	Size       int                `json:"size"`
	TotalPages int                `json:"total_pages"`
}

// TaskStatsResponse 任务统计响应
type TaskStatsResponse struct {
	TotalTasks     int64 `json:"total_tasks"`
	RunningTasks   int64 `json:"running_tasks"`
	CompletedTasks int64 `json:"completed_tasks"`
	FailedTasks    int64 `json:"failed_tasks"`
	PendingTasks   int64 `json:"pending_tasks"`
	TotalVulns     int64 `json:"total_vulns"`
}

// CreateTask 创建扫描任务
func (h *ScanHandler) CreateTask(c *gin.Context) {
	var req CreateTaskRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证扫描类型
	if !isValidScanType(req.Type) {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "不支持的扫描类型",
		})
		return
	}

	// 获取用户ID（暂时使用默认值，后续从JWT中间件获取）
	// 暂时设置为nil，避免外键约束问题
	var userID *uint = nil // 暂时设置为nil
	// userID, exists := c.Get("user_id")
	// if !exists {
	// 	c.JSON(http.StatusUnauthorized, gin.H{"error": "用户未认证"})
	// 	return
	// }

	// 设置默认值
	if req.Depth == 0 {
		req.Depth = 2
	}
	if req.Timeout == 0 || req.Timeout <= 300 {
		// 如果超时时间为0或小于等于5分钟，使用默认的2小时
		// 这样可以强制所有短超时任务使用合理的超时时间
		req.Timeout = 7200 // 默认2小时（7200秒）
	}
	if req.Threads == 0 {
		req.Threads = 5
	}
	if req.ScheduleType == "" {
		req.ScheduleType = "immediate"
	}

	// 序列化目标和配置
	targetsJSON, _ := json.Marshal(req.Targets)
	configJSON, _ := json.Marshal(req.Config)

	// 处理AssetID（如果为0则设置为nil）
	var assetID *uint
	if req.AssetID > 0 {
		assetID = &req.AssetID
	}

	// 创建扫描任务模型
	task := &models.ScanTask{
		Name:         req.Name,
		Type:         req.Type,
		Description:  req.Description,
		Targets:      string(targetsJSON),
		AssetID:      assetID,
		Config:       string(configJSON),
		Depth:        req.Depth,
		Timeout:      req.Timeout,
		Threads:      req.Threads,
		ScheduleType: req.ScheduleType,
		ScheduledAt:  req.ScheduledAt,
		CronExpr:     req.CronExpr,
		Status:       "pending",
		CreatedBy:    userID,
	}

	// 保存到数据库
	if err := h.scanService.CreateTask(task); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建任务失败",
			"error":   err.Error(),
		})
		return
	}

	// 根据调度类型处理任务
	switch req.ScheduleType {
	case "immediate":
		// 立即执行，启动扫描引擎
		err := h.startScanEngine(task, req.Targets)
		if err != nil {
			// 如果启动失败，更新任务状态为失败
			task.Status = "failed"
			task.ErrorMessage = fmt.Sprintf("启动扫描引擎失败: %v", err)
			h.scanService.UpdateTask(task)

			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "启动扫描失败",
				"error":   err.Error(),
			})
			return
		}

		// 更新任务状态为运行中
		task.Status = "running"
		now := time.Now()
		task.StartTime = &now
		h.scanService.UpdateTask(task)

	case "scheduled", "recurring":
		// 定时任务，添加到定时扫描服务
		if h.scheduledScanService != nil {
			if err := h.scheduledScanService.AddScheduledTask(task); err != nil {
				logger.Errorf("添加定时任务失败: %v", err)
				// 不返回错误，任务已创建，只是调度失败
			} else {
				logger.Infof("定时任务已添加到调度器: %s (ID: %d)", task.Name, task.ID)
			}
		} else {
			logger.Warn("定时扫描服务未初始化，无法调度定时任务")
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "扫描任务创建成功",
		"data":    task,
	})
}

// GetScheduledTasks 获取定时任务列表
func (h *ScanHandler) GetScheduledTasks(c *gin.Context) {
	if h.scheduledScanService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"code":    503,
			"message": "定时扫描服务未启用",
		})
		return
	}

	tasks, err := h.scheduledScanService.GetScheduledTasks()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取定时任务失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取定时任务成功",
		"data":    tasks,
	})
}

// GetScheduledTaskStats 获取定时任务统计
func (h *ScanHandler) GetScheduledTaskStats(c *gin.Context) {
	if h.scheduledScanService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"code":    503,
			"message": "定时扫描服务未启用",
		})
		return
	}

	stats, err := h.scheduledScanService.GetTaskStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取定时任务统计失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取定时任务统计成功",
		"data":    stats,
	})
}

// RemoveScheduledTask 移除定时任务
func (h *ScanHandler) RemoveScheduledTask(c *gin.Context) {
	if h.scheduledScanService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"code":    503,
			"message": "定时扫描服务未启用",
		})
		return
	}

	// 获取任务ID
	taskID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的任务ID",
		})
		return
	}

	// 从调度器中移除
	if err := h.scheduledScanService.RemoveScheduledTask(uint(taskID)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "移除定时任务失败",
			"error":   err.Error(),
		})
		return
	}

	// 更新数据库中的任务状态
	task, err := h.scanService.GetTaskByID(uint(taskID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "任务不存在",
		})
		return
	}

	task.Status = "cancelled"
	if err := h.scanService.UpdateTask(task); err != nil {
		logger.Errorf("更新任务状态失败: %v", err)
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "定时任务已移除",
	})
}

// GetTasks 获取扫描任务列表
func (h *ScanHandler) GetTasks(c *gin.Context) {
	// 获取查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "20"))
	scanType := c.Query("type")
	status := c.Query("status")
	keyword := c.Query("keyword")

	// 参数验证
	if page < 1 {
		page = 1
	}
	if size < 1 || size > 100 {
		size = 20
	}

	// 获取任务列表
	tasks, total, err := h.scanService.GetTasks(page, size, scanType, status, keyword)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取任务列表失败",
			"error":   err.Error(),
		})
		return
	}

	// 计算总页数
	totalPages := int(total) / size
	if int(total)%size > 0 {
		totalPages++
	}

	response := TaskListResponse{
		Tasks:      tasks,
		Total:      total,
		Page:       page,
		Size:       size,
		TotalPages: totalPages,
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    response,
	})
}

// GetTaskByID 根据ID获取扫描任务详情
func (h *ScanHandler) GetTaskByID(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的任务ID",
		})
		return
	}

	task, err := h.scanService.GetTaskByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "任务不存在",
		})
		return
	}

	// 获取详细统计信息
	statistics, err := h.scanService.GetTaskDetailedStatistics(uint(id))
	if err != nil {
		// 如果获取统计信息失败，仍然返回基本任务信息，但记录错误
		logger.Errorf("获取任务统计信息失败: %v", err)
		statistics = nil
	}

	// 构建响应数据
	responseData := gin.H{
		"task":       task,
		"statistics": statistics,
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    responseData,
	})
}

// GetTaskTargetInfo 获取扫描任务的目标信息详情
func (h *ScanHandler) GetTaskTargetInfo(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的任务ID",
		})
		return
	}

	// 获取任务基本信息
	task, err := h.scanService.GetTaskByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "任务不存在",
		})
		return
	}

	// 获取目标信息详情
	targetInfo, err := h.scanService.GetTaskTargetInfo(uint(id))
	if err != nil {
		logger.Errorf("获取任务目标信息失败: %v", err)
		// 如果获取失败，返回基于任务配置的模拟数据
		targetInfo = h.generateMockTargetInfo(task)
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    targetInfo,
	})
}

// generateMockTargetInfo 生成模拟的目标信息数据
func (h *ScanHandler) generateMockTargetInfo(task *models.ScanTask) interface{} {
	// 解析扫描目标
	var targets []string
	if err := json.Unmarshal([]byte(task.Targets), &targets); err != nil {
		targets = []string{task.Targets}
	}

	targetInfoList := make([]map[string]interface{}, 0)

	for i, target := range targets {
		targetInfo := map[string]interface{}{
			"id":            i + 1,
			"target":        target,
			"scan_type":     task.Type,
			"status":        "completed",
			"discovered_at": task.CreatedAt,
		}

		// 根据扫描类型生成相应的信息
		switch task.Type {
		case "web_scan":
			targetInfo["basic_info"] = map[string]interface{}{
				"url":           target,
				"domain":        extractDomain(target),
				"ip":            fmt.Sprintf("192.168.1.%d", 100+i),
				"port":          getPortFromURL(target),
				"protocol":      getProtocolFromURL(target),
				"status_code":   200,
				"title":         "网站标题",
				"content_type":  "text/html",
				"response_time": 150 + i*10,
			}
			targetInfo["tech_stack"] = map[string]interface{}{
				"web_server":   "Apache/2.4.41",
				"framework":    "Laravel",
				"language":     "PHP",
				"database":     "MySQL",
				"cms":          "WordPress",
				"technologies": []string{"jQuery", "Bootstrap", "Font Awesome"},
			}
			targetInfo["services"] = map[string]interface{}{
				"open_ports": []map[string]interface{}{
					{"port": 80, "protocol": "tcp", "service": "http", "version": "Apache 2.4.41", "state": "open"},
					{"port": 443, "protocol": "tcp", "service": "https", "version": "Apache 2.4.41", "state": "open"},
				},
				"ssl_info": map[string]interface{}{
					"enabled":    true,
					"version":    "TLSv1.3",
					"cipher":     "TLS_AES_256_GCM_SHA384",
					"valid_from": "2024-01-01",
					"valid_to":   "2025-01-01",
				},
			}
			targetInfo["security_config"] = map[string]interface{}{
				"security_headers": map[string]interface{}{
					"x_frame_options":           "DENY",
					"x_content_type_options":    "nosniff",
					"x_xss_protection":          "1; mode=block",
					"strict_transport_security": "max-age=31536000",
				},
				"missing_headers": []string{"Content-Security-Policy", "Referrer-Policy"},
			}
			targetInfo["directory_structure"] = map[string]interface{}{
				"discovered_paths": []string{"/admin", "/api", "/uploads", "/assets"},
				"sensitive_files":  []string{"/robots.txt", "/sitemap.xml"},
				"backup_files":     []string{},
			}
			targetInfo["crawler_findings"] = map[string]interface{}{
				"total_urls":   125,
				"forms_found":  8,
				"parameters":   []string{"id", "name", "email", "search"},
				"technologies": []string{"Google Analytics", "jQuery", "Bootstrap"},
			}

		case "port_scan":
			targetInfo["basic_info"] = map[string]interface{}{
				"ip":       target,
				"hostname": fmt.Sprintf("host-%s.local", strings.ReplaceAll(target, ".", "-")),
			}
			targetInfo["services"] = map[string]interface{}{
				"open_ports": []map[string]interface{}{
					{"port": 22, "protocol": "tcp", "service": "ssh", "version": "OpenSSH 7.4", "state": "open"},
					{"port": 80, "protocol": "tcp", "service": "http", "version": "Apache 2.4.6", "state": "open"},
					{"port": 443, "protocol": "tcp", "service": "https", "version": "Apache 2.4.6", "state": "open"},
					{"port": 3306, "protocol": "tcp", "service": "mysql", "version": "MySQL 5.7", "state": "open"},
				},
			}
			targetInfo["os_detection"] = map[string]interface{}{
				"os_family":  "Linux",
				"os_name":    "CentOS Linux 7",
				"confidence": 95,
			}

		case "host_scan":
			targetInfo["basic_info"] = map[string]interface{}{
				"ip":       extractIP(target),
				"hostname": extractDomain(target),
			}
			targetInfo["services"] = map[string]interface{}{
				"open_ports": []map[string]interface{}{
					{"port": 22, "protocol": "tcp", "service": "ssh", "version": "OpenSSH 8.0", "state": "open"},
					{"port": 80, "protocol": "tcp", "service": "http", "version": "nginx 1.18", "state": "open"},
				},
			}
			targetInfo["os_detection"] = map[string]interface{}{
				"os_family":  "Linux",
				"os_name":    "Ubuntu 20.04",
				"confidence": 90,
			}
			targetInfo["security_scan"] = map[string]interface{}{
				"weak_passwords":  []string{},
				"open_shares":     []string{},
				"vulnerabilities": []string{"CVE-2021-44228", "CVE-2021-45046"},
			}

		case "api_scan":
			targetInfo["basic_info"] = map[string]interface{}{
				"url":    target,
				"domain": extractDomain(target),
				"ip":     fmt.Sprintf("192.168.1.%d", 100+i),
			}
			targetInfo["api_endpoints"] = []map[string]interface{}{
				{"path": "/api/v1/users", "method": "GET", "auth_required": true},
				{"path": "/api/v1/login", "method": "POST", "auth_required": false},
				{"path": "/api/v1/data", "method": "GET", "auth_required": true},
			}
			targetInfo["authentication"] = map[string]interface{}{
				"auth_methods": []string{"JWT", "API Key"},
				"weak_auth":    false,
			}
		}

		// 添加统计信息
		targetInfo["statistics"] = map[string]interface{}{
			"scan_duration":      300 + i*50,
			"requests_sent":      1000 + i*200,
			"responses_received": 950 + i*180,
			"errors_count":       5 + i,
		}

		targetInfoList = append(targetInfoList, targetInfo)
	}

	return map[string]interface{}{
		"targets":     targetInfoList,
		"total_count": len(targetInfoList),
		"scan_summary": map[string]interface{}{
			"total_targets":         len(targetInfoList),
			"completed_targets":     len(targetInfoList),
			"failed_targets":        0,
			"total_ports_found":     15,
			"total_services_found":  8,
			"total_vulnerabilities": 3,
		},
	}
}

// 辅助函数：从URL中提取域名
func extractDomain(target string) string {
	if strings.HasPrefix(target, "http://") || strings.HasPrefix(target, "https://") {
		if u, err := url.Parse(target); err == nil {
			return u.Hostname()
		}
	}
	// 如果不是URL格式，直接返回
	return target
}

// 辅助函数：从URL中提取IP（这里返回模拟IP）
func extractIP(target string) string {
	// 如果已经是IP格式
	if strings.Contains(target, ".") && !strings.Contains(target, "/") {
		return target
	}
	// 否则返回模拟IP
	return "*************"
}

// 辅助函数：从URL中获取端口
func getPortFromURL(target string) int {
	if u, err := url.Parse(target); err == nil {
		if u.Port() != "" {
			if port, err := strconv.Atoi(u.Port()); err == nil {
				return port
			}
		}
		// 根据协议返回默认端口
		if u.Scheme == "https" {
			return 443
		}
		return 80
	}
	return 80
}

// 辅助函数：从URL中获取协议
func getProtocolFromURL(target string) string {
	if u, err := url.Parse(target); err == nil {
		return u.Scheme
	}
	return "http"
}

// GetTaskSiteMap 获取扫描任务的网站地图数据
func (h *ScanHandler) GetTaskSiteMap(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的任务ID",
		})
		return
	}

	// 检查任务是否存在
	task, err := h.scanService.GetTaskByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "任务不存在",
		})
		return
	}

	// 检查任务类型是否为Web扫描
	if task.Type != "web" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "只有Web扫描任务才支持网站地图功能",
		})
		return
	}

	// 检查任务是否已完成
	if task.Status != "completed" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "任务尚未完成，无法获取网站地图",
		})
		return
	}

	// 获取网站地图数据（暂时返回模拟数据）
	siteMapData := h.generateMockSiteMapData(task)

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    siteMapData,
	})
}

// generateMockSiteMapData 生成模拟的网站地图数据
func (h *ScanHandler) generateMockSiteMapData(task *models.ScanTask) map[string]interface{} {
	return map[string]interface{}{
		"totalPages": 25,
		"totalDirs":  8,
		"totalFiles": 17,
		"maxDepth":   3,
		"directoryTree": map[string]interface{}{
			"path": "/",
			"name": "root",
			"type": "directory",
			"children": []map[string]interface{}{
				{
					"path":  "/admin",
					"name":  "admin",
					"type":  "directory",
					"depth": 1,
					"children": []map[string]interface{}{
						{
							"path":       "/admin/login.php",
							"name":       "login.php",
							"type":       "file",
							"extension":  ".php",
							"statusCode": 200,
							"depth":      2,
						},
						{
							"path":       "/admin/dashboard.php",
							"name":       "dashboard.php",
							"type":       "file",
							"extension":  ".php",
							"statusCode": 403,
							"depth":      2,
						},
					},
				},
				{
					"path":  "/assets",
					"name":  "assets",
					"type":  "directory",
					"depth": 1,
					"children": []map[string]interface{}{
						{
							"path":  "/assets/css",
							"name":  "css",
							"type":  "directory",
							"depth": 2,
							"children": []map[string]interface{}{
								{
									"path":       "/assets/css/style.css",
									"name":       "style.css",
									"type":       "file",
									"extension":  ".css",
									"statusCode": 200,
									"depth":      3,
								},
							},
						},
						{
							"path":  "/assets/js",
							"name":  "js",
							"type":  "directory",
							"depth": 2,
							"children": []map[string]interface{}{
								{
									"path":       "/assets/js/app.js",
									"name":       "app.js",
									"type":       "file",
									"extension":  ".js",
									"statusCode": 200,
									"depth":      3,
								},
							},
						},
					},
				},
				{
					"path":  "/api",
					"name":  "api",
					"type":  "directory",
					"depth": 1,
					"children": []map[string]interface{}{
						{
							"path":       "/api/users.json",
							"name":       "users.json",
							"type":       "file",
							"extension":  ".json",
							"statusCode": 200,
							"depth":      2,
						},
					},
				},
			},
		},
		"filesByType": map[string]interface{}{
			"web_pages": []map[string]interface{}{
				{"name": "index.html", "path": "/index.html", "extension": ".html"},
				{"name": "login.php", "path": "/admin/login.php", "extension": ".php"},
				{"name": "dashboard.php", "path": "/admin/dashboard.php", "extension": ".php"},
			},
			"stylesheets": []map[string]interface{}{
				{"name": "style.css", "path": "/assets/css/style.css", "extension": ".css"},
				{"name": "admin.css", "path": "/admin/admin.css", "extension": ".css"},
			},
			"javascript": []map[string]interface{}{
				{"name": "app.js", "path": "/assets/js/app.js", "extension": ".js"},
				{"name": "jquery.min.js", "path": "/assets/js/jquery.min.js", "extension": ".js"},
			},
			"data_files": []map[string]interface{}{
				{"name": "users.json", "path": "/api/users.json", "extension": ".json"},
				{"name": "config.xml", "path": "/config.xml", "extension": ".xml"},
			},
		},
		"urlsByDepth": map[string]interface{}{
			"0": []string{"http://example.com/"},
			"1": []string{"http://example.com/admin/", "http://example.com/assets/", "http://example.com/api/"},
			"2": []string{
				"http://example.com/admin/login.php",
				"http://example.com/assets/css/",
				"http://example.com/assets/js/",
				"http://example.com/api/users.json",
			},
			"3": []string{
				"http://example.com/assets/css/style.css",
				"http://example.com/assets/js/app.js",
			},
		},
	}
}

// UpdateTask 更新扫描任务
func (h *ScanHandler) UpdateTask(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的任务ID",
		})
		return
	}

	var req UpdateTaskRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 获取现有任务
	task, err := h.scanService.GetTaskByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "任务不存在",
		})
		return
	}

	// 更新字段
	if req.Name != "" {
		task.Name = req.Name
	}
	if req.Description != "" {
		task.Description = req.Description
	}
	if req.Status != "" {
		task.Status = req.Status
	}
	if req.Priority > 0 {
		task.Priority = req.Priority
	}
	if req.Config != nil {
		configJSON, _ := json.Marshal(req.Config)
		task.Config = string(configJSON)
	}

	// 保存更新
	if err := h.scanService.UpdateTask(task); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新任务失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    task,
	})
}

// DeleteTask 删除扫描任务
func (h *ScanHandler) DeleteTask(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的任务ID",
		})
		return
	}

	// 检查任务是否存在
	task, err := h.scanService.GetTaskByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "任务不存在",
		})
		return
	}

	// 检查任务状态，运行中的任务不能删除
	if task.Status == "running" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "运行中的任务不能删除，请先停止任务",
		})
		return
	}

	// 删除任务
	if err := h.scanService.DeleteTask(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "删除任务失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}

// StartTask 启动扫描任务
func (h *ScanHandler) StartTask(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的任务ID",
		})
		return
	}

	// 获取任务
	task, err := h.scanService.GetTaskByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "任务不存在",
		})
		return
	}

	// 检查任务状态
	if task.Status != "pending" && task.Status != "stopped" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "只有等待中或已停止的任务可以启动",
		})
		return
	}

	// 解析目标
	var targets []string
	if err := json.Unmarshal([]byte(task.Targets), &targets); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "解析扫描目标失败",
			"error":   err.Error(),
		})
		return
	}

	// 启动扫描引擎
	err = h.startScanEngine(task, targets)
	if err != nil {
		// 如果启动失败，更新任务状态为失败
		task.Status = "failed"
		task.ErrorMessage = fmt.Sprintf("启动扫描引擎失败: %v", err)
		h.scanService.UpdateTask(task)

		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "启动扫描失败",
			"error":   err.Error(),
		})
		return
	}

	// 更新任务状态
	task.Status = "running"
	now := time.Now()
	task.StartTime = &now
	task.Progress = 0

	if err := h.scanService.UpdateTask(task); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "启动任务失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "任务启动成功",
		"data":    task,
	})
}

// StopTask 停止扫描任务
func (h *ScanHandler) StopTask(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的任务ID",
		})
		return
	}

	// 获取任务
	task, err := h.scanService.GetTaskByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "任务不存在",
		})
		return
	}

	// 检查任务状态
	if task.Status != "running" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "只有运行中的任务可以停止",
		})
		return
	}

	// 更新任务状态
	task.Status = "stopped"
	now := time.Now()
	task.EndTime = &now

	// 计算执行时间
	if task.StartTime != nil {
		duration := now.Sub(*task.StartTime)
		task.Duration = int(duration.Seconds())
	}

	if err := h.scanService.UpdateTask(task); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "停止任务失败",
			"error":   err.Error(),
		})
		return
	}

	// TODO: 这里应该调用扫描引擎停止实际的扫描工作

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "任务停止成功",
		"data":    task,
	})
}

// GetTaskStats 获取扫描任务统计信息
func (h *ScanHandler) GetTaskStats(c *gin.Context) {
	stats, err := h.scanService.GetTaskStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取统计信息失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    stats,
	})
}

// isValidScanType 验证扫描类型是否有效
func isValidScanType(scanType string) bool {
	validTypes := []string{
		"web", "network", "host", "api", "compliance", "domain",
		"discovery", "network_discovery", "port_discovery", "subdomain_discovery",
	}
	for _, t := range validTypes {
		if t == scanType {
			return true
		}
	}
	return false
}

// startScanEngine 启动扫描引擎
func (h *ScanHandler) startScanEngine(task *models.ScanTask, targets []string) error {
	fmt.Printf("=== [startScanEngine] 开始启动扫描引擎，任务ID: %d ===\n", task.ID)

	// 记录扫描任务启动日志（保留历史日志，不进行清理）
	h.scanLogService.LogInfo(task.ID, "初始化", "",
		fmt.Sprintf("开始启动%s扫描任务，目标数量: %d", task.Type, len(targets)), 0)

	fmt.Printf("[startScanEngine] 开始为 %d 个目标创建和下发扫描任务\n", len(targets))

	// 为每个目标创建扫描任务
	for i, targetStr := range targets {
		fmt.Printf("[startScanEngine] 正在处理目标 %d/%d: %s\n", i+1, len(targets), targetStr)
		// 记录目标处理日志
		h.scanLogService.LogDebug(task.ID, "初始化", targetStr,
			fmt.Sprintf("处理扫描目标 %d/%d: %s", i+1, len(targets), targetStr), "", 0)

		// 创建扫描目标
		target := &scanner.ScanTarget{
			ID:    fmt.Sprintf("%d_%d", task.ID, i),
			Type:  task.Type,
			Value: targetStr,
		}

		// 解析扫描配置
		var config map[string]interface{}
		if task.Config != "" {
			if err := json.Unmarshal([]byte(task.Config), &config); err != nil {
				h.scanLogService.LogError(task.ID, "初始化", targetStr,
					"解析扫描配置失败", err.Error(), 0)
				return fmt.Errorf("解析扫描配置失败: %v", err)
			}
		}

		// 创建扫描配置
		scanConfig := &scanner.ScanConfig{
			TaskID:    fmt.Sprintf("%d", task.ID),
			Depth:     task.Depth,
			Timeout:   time.Duration(task.Timeout) * time.Second,
			Threads:   task.Threads,
			UserAgent: "Scanner/1.0",
		}

		// 根据扫描类型设置具体配置
		switch task.Type {
		case "web":
			h.scanLogService.LogInfo(task.ID, "配置", targetStr,
				"配置Web扫描引擎参数", 5)
			scanConfig.WebConfig = &scanner.WebScanConfig{
				CheckSQLi:         true,
				CheckXSS:          true,
				CheckCSRF:         true,
				CheckFileUpload:   true,
				CheckDirTraversal: true,
				CheckWeakAuth:     true,
				CrawlDepth:        task.Depth,
			}
		case "network":
			h.scanLogService.LogInfo(task.ID, "配置", targetStr,
				fmt.Sprintf("配置网络扫描引擎参数，线程数: %d，超时: %ds", task.Threads, task.Timeout), 5)
			scanConfig.NetworkConfig = &scanner.NetworkScanConfig{
				PortRange:        "1-65535",
				ScanTCP:          true,
				ScanUDP:          false,
				ServiceDetection: true,
				OSDetection:      false,
				BannerGrab:       true,
			}
		case "host":
			h.scanLogService.LogInfo(task.ID, "配置", targetStr,
				"配置主机扫描引擎参数", 5)
			scanConfig.HostConfig = &scanner.HostScanConfig{
				CheckSystemInfo: true,
				CheckProcesses:  true,
				CheckServices:   true,
				CheckFiles:      true,
				CheckUsers:      true,
				CheckPatches:    true,
			}
		case "api":
			h.scanLogService.LogInfo(task.ID, "配置", targetStr,
				"配置API扫描引擎参数", 5)
			scanConfig.APIConfig = &scanner.APIScanConfig{
				CheckAuthBypass: true,
				CheckInjection:  true,
				CheckRateLimit:  true,
				CheckDataLeak:   true,
				CheckBizLogic:   true,
			}
		case "domain", "subdomain_discovery":
			h.scanLogService.LogInfo(task.ID, "配置", targetStr,
				"配置域名发现引擎参数", 5)
			// 域名发现和子域名发现直接使用基础配置，不需要特殊配置
			// 域名引擎会根据基础配置自动进行子域名发现
		case "network_discovery", "port_discovery":
			h.scanLogService.LogInfo(task.ID, "配置", targetStr,
				"配置网络发现引擎参数", 5)
			// 网络发现和端口发现使用网络扫描配置
			scanConfig.NetworkConfig = &scanner.NetworkScanConfig{
				PortRange:        "1-1000", // 发现模式使用较小端口范围
				ScanTCP:          true,
				ScanUDP:          false,
				ServiceDetection: true,
				OSDetection:      false,
				BannerGrab:       true,
			}
		case "discovery":
			h.scanLogService.LogInfo(task.ID, "配置", targetStr,
				"配置资产发现引擎参数", 5)
			// 资产发现使用综合配置
			scanConfig.NetworkConfig = &scanner.NetworkScanConfig{
				PortRange:        "1-1000",
				ScanTCP:          true,
				ScanUDP:          false,
				ServiceDetection: true,
				OSDetection:      true,
				BannerGrab:       true,
			}
		case "compliance":
			h.scanLogService.LogInfo(task.ID, "配置", targetStr,
				"配置合规检测引擎参数", 5)
			// 合规检测使用主机扫描配置
			scanConfig.HostConfig = &scanner.HostScanConfig{
				CheckSystemInfo: true,
				CheckProcesses:  true,
				CheckServices:   true,
				CheckFiles:      true,
				CheckUsers:      true,
				CheckPatches:    true,
			}
		}

		// 记录扫描引擎启动日志
		h.scanLogService.LogInfo(task.ID, "启动", targetStr,
			fmt.Sprintf("启动%s扫描引擎", task.Type), 10)

		// 通过任务调度器启动扫描
		fmt.Printf("[startScanEngine] 正在调度扫描任务，目标: %s\n", targetStr)
		scheduledTask, err := h.taskScheduler.ScheduleTask(target, scanConfig)
		if err != nil {
			fmt.Printf("[startScanEngine] 调度任务失败: %v\n", err)
			h.scanLogService.LogError(task.ID, "启动", targetStr,
				"调度扫描任务失败", err.Error(), 0)
			return fmt.Errorf("调度扫描任务失败: %v", err)
		}

		fmt.Printf("[startScanEngine] 任务调度成功，调度ID: %s, 任务ID: %s\n", scheduledTask.ID, scheduledTask.TaskID)
		// 记录成功启动日志
		h.scanLogService.LogInfo(task.ID, "启动", targetStr,
			fmt.Sprintf("扫描引擎启动成功，调度ID: %s, 任务ID: %s", scheduledTask.ID, scheduledTask.TaskID), 15)

		// 启动真实的进度监听协程
		go h.monitorRealScanProgress(task.ID, targetStr, scheduledTask)
	}

	// 记录所有目标启动完成日志
	fmt.Printf("[startScanEngine] 所有 %d 个扫描目标启动完成\n", len(targets))
	h.scanLogService.LogInfo(task.ID, "启动", "",
		fmt.Sprintf("所有扫描目标启动完成，共 %d 个目标", len(targets)), 20)

	fmt.Printf("=== [startScanEngine] 扫描引擎启动流程完成 ===\n")
	return nil
}

// monitorRealScanProgress 监控真实扫描进度
func (h *ScanHandler) monitorRealScanProgress(taskID uint, target string, scheduledTask *scanner.ScheduledTask) {
	// 记录开始监控日志
	h.scanLogService.LogInfo(taskID, "监控", target, "开始监控扫描进度", 0)

	// 优化监控机制 - 减少资源消耗
	ticker := time.NewTicker(10 * time.Second) // 增加检查间隔为10秒，减少资源消耗
	defer ticker.Stop()

	timeout := time.After(30 * time.Minute) // 缩短超时时间到30分钟，避免长时间监控
	maxChecks := 180                        // 最大检查次数（30分钟 * 6次/分钟）
	checkCount := 0
	consecutiveFailures := 0    // 连续失败次数
	maxConsecutiveFailures := 5 // 减少最大连续失败次数
	lastStatus := ""            // 记录上次状态，避免重复日志
	noChangeCount := 0          // 连续无变化次数
	maxNoChangeCount := 12      // 最大无变化次数（2分钟）

	for {
		select {
		case <-ticker.C:
			checkCount++

			// 优化：减少DEBUG日志频率，只在每10次检查时记录一次
			if checkCount%10 == 0 {
				h.scanLogService.LogDebug(taskID, "监控", target,
					fmt.Sprintf("监控检查 %d/%d，调度ID: %s，任务ID: %s", checkCount, maxChecks, scheduledTask.ID, scheduledTask.TaskID), "", 0)
			}

			// 检查调度任务状态
			status, err := h.taskScheduler.GetTaskStatus(scheduledTask.ID)
			if err != nil {
				consecutiveFailures++
				// 只在失败时记录DEBUG日志
				if consecutiveFailures <= 3 {
					h.scanLogService.LogDebug(taskID, "监控", target,
						fmt.Sprintf("调度任务状态查询失败: %v，连续失败次数: %d/%d", err, consecutiveFailures, maxConsecutiveFailures), "", 0)
				}

				// 如果连续失败次数过多，检查引擎状态
				if consecutiveFailures >= maxConsecutiveFailures {
					h.scanLogService.LogWarn(taskID, "监控", target,
						fmt.Sprintf("连续%d次无法获取调度任务状态，尝试检查引擎状态", consecutiveFailures), "", 0)

					engineStatus, engineErr := h.engineManager.GetTaskStatus(scheduledTask.TaskID)
					if engineErr != nil {
						h.scanLogService.LogWarn(taskID, "监控", target,
							"引擎任务状态查询也失败，假设任务已完成", "", 100)

						// 强制完成任务
						if err := h.scanService.UpdateTaskStatus(taskID, "completed"); err != nil {
							h.scanLogService.LogError(taskID, "完成", target,
								"更新任务状态失败", err.Error(), 100)
						}
						if err := h.scanService.UpdateTaskProgress(taskID, 100, "完成", "监控超时，强制完成"); err != nil {
							h.scanLogService.LogError(taskID, "完成", target,
								"更新任务进度失败", err.Error(), 100)
						}
						return
					}
					status = engineStatus
					consecutiveFailures = 0 // 重置失败计数
				} else {
					// 继续监控，但不立即返回
					continue
				}
			} else {
				consecutiveFailures = 0 // 重置失败计数
			}

			// 优化：只在状态变化时记录INFO日志，避免重复日志
			if status != lastStatus {
				h.scanLogService.LogInfo(taskID, "监控", target,
					fmt.Sprintf("任务状态变化: %s -> %s (检查 %d/%d)", lastStatus, status, checkCount, maxChecks), 0)
				lastStatus = status
				noChangeCount = 0
			} else {
				noChangeCount++
				// 如果状态长时间无变化，可能任务已经卡住
				if noChangeCount >= maxNoChangeCount {
					h.scanLogService.LogWarn(taskID, "监控", target,
						fmt.Sprintf("任务状态长时间无变化: %s，已检查 %d 次", status, noChangeCount), "", 0)

					// 检查引擎状态确认任务是否真的还在运行
					engineStatus, engineErr := h.engineManager.GetTaskStatus(scheduledTask.TaskID)
					if engineErr != nil || engineStatus == "completed" || engineStatus == "failed" {
						h.scanLogService.LogInfo(taskID, "监控", target,
							"检测到任务可能已完成，结束监控", 100)
						// 强制完成任务
						if err := h.scanService.UpdateTaskStatus(taskID, "completed"); err == nil {
							h.scanService.UpdateTaskProgress(taskID, 100, "完成", "监控检测完成")
						}
						return
					}
					noChangeCount = 0 // 重置计数器
				}
			}

			// 如果任务完成或失败，退出监控
			if status == "completed" || status == "failed" || status == "cancelled" {
				// 更新数据库中的任务状态
				var finalStatus string
				var progress int

				if status == "completed" {
					finalStatus = "completed"
					progress = 100

					// 处理扫描结果 - 保存发现的资产和漏洞
					h.scanLogService.LogInfo(taskID, "处理结果", target,
						"开始处理扫描结果，保存发现的资产和漏洞", progress)

					if err := h.processScanResults(taskID, target, scheduledTask); err != nil {
						h.scanLogService.LogError(taskID, "处理结果", target,
							"处理扫描结果失败", err.Error(), progress)
					} else {
						h.scanLogService.LogInfo(taskID, "处理结果", target,
							"扫描结果处理完成", progress)
					}
				} else {
					finalStatus = "failed"
					progress = 0
				}

				h.scanLogService.LogInfo(taskID, "监控", target,
					fmt.Sprintf("任务状态变为: %s，开始更新数据库", finalStatus), progress)

				if err := h.scanService.UpdateTaskStatus(taskID, finalStatus); err != nil {
					h.scanLogService.LogError(taskID, "完成", target,
						"更新任务状态失败", err.Error(), progress)
				} else {
					h.scanLogService.LogInfo(taskID, "完成", target,
						fmt.Sprintf("扫描任务%s，数据库状态已更新", finalStatus), progress)
				}

				// 更新进度
				if err := h.scanService.UpdateTaskProgress(taskID, progress, "完成", fmt.Sprintf("扫描任务%s", finalStatus)); err != nil {
					h.scanLogService.LogError(taskID, "完成", target,
						"更新任务进度失败", err.Error(), progress)
				}

				return
			}

			// 检查是否超过最大检查次数
			if checkCount >= maxChecks {
				h.scanLogService.LogWarn(taskID, "监控", target,
					"监控检查次数接近上限", fmt.Sprintf("已检查 %d/%d 次，进行最后状态确认", checkCount, maxChecks), 0)

				// 最后一次状态确认 - 检查任务是否真的还在运行
				finalStatus, err := h.taskScheduler.GetTaskStatus(scheduledTask.ID)
				if err == nil && (finalStatus == "completed" || finalStatus == "failed" || finalStatus == "stopped") {
					h.scanLogService.LogInfo(taskID, "监控", target,
						fmt.Sprintf("任务已完成，最终状态: %s", finalStatus), 0)
					return
				}

				// 检查引擎状态
				engineStatus, engineErr := h.engineManager.GetTaskStatus(scheduledTask.TaskID)
				if engineErr == nil && (engineStatus == "completed" || engineStatus == "failed" || engineStatus == "stopped") {
					h.scanLogService.LogInfo(taskID, "监控", target,
						fmt.Sprintf("引擎任务已完成，引擎状态: %s", engineStatus), 0)
					return
				}

				// 如果任务确实还在运行，记录警告但不强制结束
				h.scanLogService.LogError(taskID, "监控", target,
					"监控检查次数超限", "达到最大检查次数，但任务可能仍在运行，强制结束", 0)

				// 更新任务状态为失败
				if err := h.scanService.UpdateTaskStatus(taskID, "failed"); err != nil {
					h.scanLogService.LogError(taskID, "监控", target,
						"更新超限任务状态失败", err.Error(), 0)
				}

				return
			}

		case <-timeout:
			// 超时处理 - 先检查任务是否真的还在运行
			h.scanLogService.LogWarn(taskID, "监控", target,
				"监控超时，检查任务状态", "监控达到2小时超时，检查任务是否仍在运行", 0)

			// 最后一次检查任务状态
			finalStatus, err := h.taskScheduler.GetTaskStatus(scheduledTask.ID)
			if err != nil {
				// 如果调度任务不存在，检查引擎状态
				engineStatus, engineErr := h.engineManager.GetTaskStatus(scheduledTask.TaskID)
				if engineErr != nil {
					// 两个地方都找不到，可能已经完成
					h.scanLogService.LogInfo(taskID, "监控", target,
						"超时检查：任务可能已完成", 100)
					if err := h.scanService.UpdateTaskStatus(taskID, "completed"); err != nil {
						h.scanLogService.LogError(taskID, "监控", target,
							"更新完成状态失败", err.Error(), 100)
					}
					return
				}
				finalStatus = engineStatus
			}

			// 如果任务已经完成，不需要取消
			if finalStatus == "completed" || finalStatus == "failed" || finalStatus == "stopped" {
				h.scanLogService.LogInfo(taskID, "监控", target,
					fmt.Sprintf("超时检查：任务已完成，状态: %s", finalStatus), 100)
				if err := h.scanService.UpdateTaskStatus(taskID, finalStatus); err != nil {
					h.scanLogService.LogError(taskID, "监控", target,
						"更新最终状态失败", err.Error(), 100)
				}
				return
			}

			// 只有在任务确实还在运行时才取消
			h.scanLogService.LogError(taskID, "监控", target,
				"扫描任务真正超时", fmt.Sprintf("任务运行超过2小时仍未完成，状态: %s", finalStatus), 0)

			// 尝试停止任务
			if err := h.taskScheduler.CancelTask(scheduledTask.ID); err != nil {
				h.scanLogService.LogError(taskID, "监控", target,
					"取消超时任务失败", err.Error(), 0)
			}

			// 更新任务状态为超时失败
			if err := h.scanService.UpdateTaskStatus(taskID, "timeout"); err != nil {
				h.scanLogService.LogError(taskID, "监控", target,
					"更新超时任务状态失败", err.Error(), 0)
			}

			return
		}
	}
}

// processScanResults 处理扫描结果
// 从扫描引擎获取扫描结果并保存到数据库
func (h *ScanHandler) processScanResults(taskID uint, target string, scheduledTask *scanner.ScheduledTask) error {
	h.scanLogService.LogInfo(taskID, "处理结果", target, "开始处理扫描结果", 0)

	// 暂时实现一个简化版本的结果处理
	// TODO: 后续需要实现完整的扫描结果获取和保存逻辑

	h.scanLogService.LogInfo(taskID, "处理结果", target, "扫描结果处理完成（当前为简化实现）", 0)

	// 这里可以添加具体的结果处理逻辑：
	// 1. 从扫描引擎获取扫描结果
	// 2. 解析发现的资产和漏洞
	// 3. 保存到数据库

	return nil
}

// GetScanTaskVulnerabilities 获取扫描任务的漏洞列表
// @Summary 获取扫描任务漏洞
// @Description 获取指定扫描任务发现的漏洞列表
// @Tags 扫描管理
// @Accept json
// @Produce json
// @Param id path int true "任务ID"
// @Param page query int false "页码" default(1)
// @Param size query int false "每页数量" default(10)
// @Param severity query string false "严重程度"
// @Success 200 {object} Response{data=[]models.Vulnerability}
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/scans/{id}/vulnerabilities [get]
func (h *ScanHandler) GetScanTaskVulnerabilities(c *gin.Context) {
	// 解析任务ID
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的任务ID",
			"data":    nil,
		})
		return
	}

	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))
	severity := c.Query("severity")
	vulnType := c.Query("type")

	// 获取漏洞列表
	vulns, total, err := h.scanService.GetTaskVulnerabilities(uint(id), page, size, severity, vulnType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取漏洞列表失败: " + err.Error(),
			"data":    nil,
		})
		return
	}

	// 返回结果
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取漏洞列表成功",
		"data": gin.H{
			"vulnerabilities": vulns,
			"pagination": gin.H{
				"page":  page,
				"size":  size,
				"total": total,
			},
		},
	})
}
